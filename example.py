#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例 - 展示如何使用四川大学期刊爬虫
"""

from find import SCUJournalCrawler

def example_usage():
    """使用示例"""
    print("四川大学期刊爬虫使用示例")
    print("=" * 40)
    
    # 创建爬虫实例
    crawler = SCUJournalCrawler()
    
    # 示例：测试页面结构（无需登录）
    print("\n1. 测试页面结构（推荐先运行此步骤）")
    print("   这将帮助您了解网站的结构，无需登录")
    
    # 示例：正常爬取流程
    print("\n2. 正常爬取流程：")
    print("   a) 设置浏览器驱动")
    print("   b) 登录网站")
    print("   c) 爬取期刊数据")
    print("   d) 保存到Excel文件")
    
    print("\n3. 运行方式：")
    print("   python find.py")
    print("   然后选择操作模式")
    
    print("\n4. 输出文件：")
    print("   - 四川大学期刊数据.xlsx")
    print("   - 四川大学期刊数据.csv")
    print("   - page_source.html (调试用)")

def manual_example():
    """手动使用示例"""
    print("\n手动使用示例：")
    print("=" * 40)
    
    # 这里展示如何手动调用各个功能
    crawler = SCUJournalCrawler()
    
    try:
        # 1. 设置浏览器
        print("1. 设置浏览器驱动...")
        if not crawler.setup_driver(headless=False):
            print("浏览器设置失败")
            return
        
        # 2. 访问页面
        print("2. 访问登录页面...")
        crawler.driver.get(crawler.login_url)
        
        # 3. 这里可以手动登录或进行其他操作
        print("3. 请在浏览器中手动操作...")
        input("完成后按回车继续...")
        
        # 4. 解析数据
        print("4. 解析页面数据...")
        journals = crawler.parse_journal_data()
        
        if journals:
            print(f"找到 {len(journals)} 条期刊数据")
            # 显示前几条数据
            for i, journal in enumerate(journals[:3]):
                print(f"期刊 {i+1}: {journal}")
        else:
            print("未找到期刊数据")
        
    except Exception as e:
        print(f"操作失败: {e}")
    finally:
        if crawler.driver:
            crawler.driver.quit()

if __name__ == "__main__":
    example_usage()
    
    # 如果需要手动测试，取消下面的注释
    # manual_example()

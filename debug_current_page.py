#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试当前页面状态
"""

from bs4 import BeautifulSoup
import re
import json

def analyze_current_page():
    """分析当前保存的页面"""
    
    print("=" * 60)
    print("分析当前页面状态")
    print("=" * 60)
    
    try:
        with open('current_page.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 1. 检查页面标题
        title = soup.find('title')
        if title:
            print(f"页面标题: {title.get_text()}")
        
        # 2. 检查是否有登录表单
        login_form = soup.find('form', {'name': 'loginForm'})
        if login_form:
            print("⚠ 发现登录表单，可能未成功登录")
        else:
            print("✓ 未发现登录表单，可能已登录")
        
        # 3. 检查表格结构
        tables = soup.find_all('table')
        print(f"找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            table_id = table.get('id', f'table_{i}')
            print(f"  表格{i+1}: id='{table_id}'")
            
            if table_id == 'main-table':
                print("    ✓ 找到主数据表格")
                tbody = table.find('tbody')
                if tbody:
                    rows = tbody.find_all('tr')
                    print(f"    表格行数: {len(rows)}")
                    
                    if rows:
                        print("    前3行数据:")
                        for j, row in enumerate(rows[:3]):
                            cells = row.find_all(['td', 'th'])
                            cell_texts = [cell.get_text().strip() for cell in cells]
                            print(f"      行{j+1}: {cell_texts}")
                else:
                    print("    ⚠ 表格没有tbody")
        
        # 4. 检查JavaScript代码中的数据URL
        script_tags = soup.find_all('script')
        print(f"\n找到 {len(script_tags)} 个script标签")
        
        data_urls = []
        for script in script_tags:
            script_text = script.get_text()
            if 'dataUrl' in script_text or '.json' in script_text:
                # 查找数据URL
                url_matches = re.findall(r'["\']([^"\']*\.json)["\']', script_text)
                data_urls.extend(url_matches)
        
        if data_urls:
            print("发现数据URL:")
            for url in set(data_urls):
                print(f"  {url}")
        
        # 5. 检查是否有错误信息
        error_elements = soup.find_all(text=re.compile(r'错误|失败|error|Error'))
        if error_elements:
            print(f"\n发现可能的错误信息:")
            for error in error_elements[:3]:  # 只显示前3个
                print(f"  {error.strip()}")
        
        # 6. 检查layui相关元素
        layui_elements = soup.find_all(class_=re.compile(r'layui'))
        print(f"\n找到 {len(layui_elements)} 个layui相关元素")
        
        # 7. 检查是否有数据加载相关的JavaScript
        js_content = ""
        for script in script_tags:
            js_content += script.get_text() + "\n"
        
        if 'json' in js_content:
            print("✓ JavaScript中包含json变量")
        if 'load()' in js_content:
            print("✓ JavaScript中包含load()函数")
        if 'ajax' in js_content:
            print("✓ JavaScript中包含ajax调用")
        
        # 8. 查找具体的数据加载逻辑
        load_function_match = re.search(r'function load\(\)\s*\{([^}]+)\}', js_content, re.DOTALL)
        if load_function_match:
            print("\n找到load()函数:")
            load_content = load_function_match.group(1)
            print(f"  {load_content.strip()[:200]}...")
        
        # 9. 检查页面是否完全加载
        if 'layui-table' in html_content:
            print("✓ 页面包含layui表格样式")
        
        if 'tbody' in html_content and 'data-field' in html_content:
            print("✓ 页面包含数据表格结构")
        
        return True
        
    except FileNotFoundError:
        print("✗ 未找到 current_page.html 文件")
        print("请先运行爬虫程序生成页面文件")
        return False
    except Exception as e:
        print(f"✗ 分析页面时出错: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n" + "=" * 60)
    print("建议解决方案")
    print("=" * 60)
    
    print("1. 登录问题:")
    print("   - 确保用户名和密码正确")
    print("   - 手动完成验证码输入")
    print("   - 检查账号是否有访问权限")
    
    print("\n2. 数据加载问题:")
    print("   - 增加等待时间，确保JavaScript执行完成")
    print("   - 手动触发数据加载函数")
    print("   - 检查网络连接是否稳定")
    
    print("\n3. 页面结构问题:")
    print("   - 网站可能更新了结构")
    print("   - 需要重新分析页面元素")
    print("   - 考虑使用不同的数据获取方法")
    
    print("\n4. 调试建议:")
    print("   - 运行 test_js_data.py 进行详细测试")
    print("   - 检查浏览器控制台是否有错误")
    print("   - 尝试手动在浏览器中访问数据URL")

def main():
    success = analyze_current_page()
    suggest_solutions()
    
    if success:
        print("\n✓ 页面分析完成")
    else:
        print("\n✗ 页面分析失败")

if __name__ == "__main__":
    main()

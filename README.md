# 四川大学期刊查询系统爬虫

这个程序用于爬取四川大学期刊查询系统（https://ir.scu.edu.cn/style/excel/views/index3.jsp）中的期刊数据，并保存到Excel文件中。

## 功能特点

- 🔐 自动登录四川大学期刊查询系统
- 📊 爬取所有期刊分类的数据
- 💾 支持保存为Excel和CSV格式
- 🔍 智能解析期刊信息（期刊全称、简称、ISSN、分类、等级等）
- 🚀 使用Selenium模拟浏览器操作，支持JavaScript渲染的页面
- ⚡ 自动去重，避免重复数据

## 环境要求

- Python 3.7+
- Chrome浏览器
- ChromeDriver（需要与Chrome版本匹配）

## 安装依赖

```bash
pip install -r requirements.txt
```

## ChromeDriver安装

1. 查看Chrome浏览器版本：在Chrome地址栏输入 `chrome://version/`
2. 下载对应版本的ChromeDriver：https://chromedriver.chromium.org/
3. 将ChromeDriver.exe放到系统PATH路径中，或者放到项目目录下

## 使用方法

### 快速开始

1. **安装依赖**（Windows用户可直接运行）：
```bash
install.bat
```

或手动安装：
```bash
pip install -r requirements.txt
```

2. **测试环境**（推荐先运行）：
```bash
python simple_test.py
```
这将分析页面结构并生成调试文件。

3. **运行程序**：
```bash
python find.py
```

4. **选择操作模式**：
   - 模式1：正常爬取模式（需要登录）
   - 模式2：测试页面结构模式（无需登录，用于调试）

5. **输入登录信息**（模式1）：
   - 用户名：一卡通号（对应页面上的userName字段）
   - 密码：图书馆借阅密码（对应页面上的password字段）

6. **处理验证码**：
   - 程序会自动检测验证码输入框（randomCode字段）
   - 请在浏览器中手动输入验证码
   - 程序会自动点击登录按钮（loginbtn1）
   - 在控制台按回车继续

7. **等待爬取完成**：
   - 程序会自动爬取期刊数据表格（main-table）
   - 数据会保存到Excel和CSV文件中

### 调试工具

- `simple_test.py` - 快速分析页面结构
- `debug_crawler.py` - 详细的交互式调试工具
- `test.py` - 环境和功能测试

## 输出文件

- `四川大学期刊数据.xlsx` - Excel格式的期刊数据
- `四川大学期刊数据.csv` - CSV格式的期刊数据

## 数据字段

爬取的期刊数据包含以下字段：
- 期刊全称
- 期刊简称
- ISSN
- 分类
- 等级
- 影响因子
- 学科领域

## 注意事项

1. 请确保您有有效的四川大学账号和密码
2. 爬取过程中请保持网络连接稳定
3. 程序会自动处理验证码，如遇到验证码请手动输入
4. 爬取速度会根据网站响应速度自动调整
5. 请遵守网站的使用条款，合理使用爬虫

## 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   - 确保ChromeDriver版本与Chrome浏览器版本匹配
   - 重新下载对应版本的ChromeDriver

2. **登录失败**
   - 检查用户名和密码是否正确
   - 确认账号是否有访问权限

3. **数据解析失败**
   - 网站结构可能发生变化，需要更新解析代码
   - 检查网络连接是否稳定

4. **依赖包安装失败**
   - 使用国内镜像源：`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`

## 技术实现

- **Selenium**: 模拟浏览器操作，处理JavaScript渲染
- **BeautifulSoup**: HTML解析和数据提取
- **Pandas**: 数据处理和Excel文件生成
- **Requests**: HTTP请求处理

## 免责声明

本程序仅供学习和研究使用，请遵守相关法律法规和网站使用条款。使用者需对使用本程序产生的任何后果承担责任。

## 许可证

MIT License

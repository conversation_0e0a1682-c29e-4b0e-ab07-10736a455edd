#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证爬虫程序的基本功能
"""

import sys
import importlib

def test_imports():
    """测试依赖包导入"""
    print("测试依赖包导入...")
    
    required_packages = [
        'requests',
        'pandas', 
        'bs4',
        'selenium',
        'openpyxl',
        'time',
        're',
        'json',
        'os'
    ]
    
    failed_imports = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✓ {package}")
        except ImportError as e:
            print(f"✗ {package}: {e}")
            failed_imports.append(package)
    
    if failed_imports:
        print(f"\n缺少以下依赖包: {', '.join(failed_imports)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n✓ 所有依赖包导入成功")
        return True

def test_selenium_setup():
    """测试Selenium设置"""
    print("\n测试Selenium设置...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.get('https://www.baidu.com')
        title = driver.title
        driver.quit()
        
        print(f"✓ Chrome浏览器驱动测试成功，访问页面标题: {title}")
        return True
        
    except Exception as e:
        print(f"✗ Chrome浏览器驱动测试失败: {e}")
        print("请确保:")
        print("1. 已安装Chrome浏览器")
        print("2. 已下载对应版本的ChromeDriver")
        print("3. ChromeDriver在系统PATH中或当前目录下")
        return False

def test_crawler_class():
    """测试爬虫类"""
    print("\n测试爬虫类...")
    
    try:
        from find import SCUJournalCrawler
        
        crawler = SCUJournalCrawler()
        print("✓ 爬虫类实例化成功")
        
        # 测试文本清理功能
        test_text = "  测试文本\n\t多余空格  "
        cleaned = crawler.clean_text(test_text)
        expected = "测试文本 多余空格"
        
        if cleaned == expected:
            print("✓ 文本清理功能正常")
        else:
            print(f"✗ 文本清理功能异常: 期望'{expected}', 实际'{cleaned}'")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 爬虫类测试失败: {e}")
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n测试数据处理功能...")
    
    try:
        import pandas as pd
        
        # 测试数据
        test_data = [
            {
                '期刊全称': 'Nature',
                '期刊简称': 'Nature',
                'ISSN': '0028-0836',
                '分类': '综合性期刊',
                '等级': 'T1'
            },
            {
                '期刊全称': 'Science',
                '期刊简称': 'Science', 
                'ISSN': '0036-8075',
                '分类': '综合性期刊',
                '等级': 'T1'
            }
        ]
        
        # 创建DataFrame
        df = pd.DataFrame(test_data)
        
        # 保存测试文件
        df.to_excel('test_output.xlsx', index=False, engine='openpyxl')
        df.to_csv('test_output.csv', index=False, encoding='utf-8-sig')
        
        print("✓ Excel和CSV文件生成成功")
        
        # 清理测试文件
        import os
        os.remove('test_output.xlsx')
        os.remove('test_output.csv')
        
        print("✓ 数据处理功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 数据处理功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("四川大学期刊爬虫 - 功能测试")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_selenium_setup,
        test_crawler_class,
        test_data_processing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过，程序可以正常运行")
        print("\n可以运行: python find.py")
    else:
        print("✗ 部分测试失败，请检查环境配置")
        print("\n请参考README.md中的安装说明")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

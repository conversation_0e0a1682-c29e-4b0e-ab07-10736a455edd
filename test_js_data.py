#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JavaScript数据获取
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time
import json

def test_js_data_extraction():
    """测试从JavaScript获取数据"""
    
    # 设置Chrome选项
    options = webdriver.ChromeOptions()
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    driver = None
    try:
        driver = webdriver.Chrome(options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("=" * 60)
        print("测试JavaScript数据获取")
        print("=" * 60)
        
        # 访问登录页面
        login_url = "https://ir.scu.edu.cn/publicUser/toLogin"
        print(f"访问登录页面: {login_url}")
        driver.get(login_url)
        
        # 等待用户手动登录
        print("\n请在浏览器中完成登录，然后在控制台按回车继续...")
        input("按回车键继续...")
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(5)
        
        # 检查当前URL
        current_url = driver.current_url
        print(f"当前URL: {current_url}")
        
        # 等待JavaScript数据加载
        print("等待JavaScript数据加载...")
        try:
            # 等待json变量存在且有数据
            WebDriverWait(driver, 30).until(
                lambda d: d.execute_script("return typeof json !== 'undefined' && json && json.length > 0")
            )
            print("✓ JavaScript数据加载完成")
            
            # 获取数据
            json_data = driver.execute_script("return json;")
            print(f"✓ 获取到 {len(json_data)} 条数据")
            
            # 显示前5条数据
            print("\n前5条数据预览:")
            for i, item in enumerate(json_data[:5]):
                print(f"第{i+1}条: {item}")
            
            # 保存数据到文件
            with open('js_data.json', 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            print(f"\n✓ 数据已保存到 js_data.json")
            
            # 分析数据结构
            if json_data:
                sample = json_data[0]
                print(f"\n数据字段: {list(sample.keys())}")
                
                # 统计各分级的数量
                grade_count = {}
                for item in json_data:
                    grade = item.get('分级', 'Unknown')
                    grade_count[grade] = grade_count.get(grade, 0) + 1
                
                print(f"\n分级统计:")
                for grade, count in sorted(grade_count.items()):
                    print(f"  {grade}: {count} 条")
            
            return True
            
        except TimeoutException:
            print("✗ JavaScript数据加载超时")
            
            # 尝试手动触发数据加载
            print("尝试手动触发数据加载...")
            try:
                driver.execute_script("if (typeof load === 'function') load();")
                time.sleep(5)
                
                json_data = driver.execute_script("return json;")
                if json_data and len(json_data) > 0:
                    print(f"✓ 手动触发后获取到 {len(json_data)} 条数据")
                    return True
                else:
                    print("✗ 手动触发后仍无数据")
                    
            except Exception as e:
                print(f"✗ 手动触发失败: {e}")
            
            # 检查页面状态
            print("\n页面状态检查:")
            try:
                # 检查是否有错误信息
                page_title = driver.title
                print(f"页面标题: {page_title}")
                
                # 检查是否有表格
                tables = driver.find_elements(By.TAG_NAME, "table")
                print(f"找到 {len(tables)} 个表格")
                
                # 检查JavaScript变量
                js_vars = driver.execute_script("""
                    return {
                        json_exists: typeof json !== 'undefined',
                        json_length: typeof json !== 'undefined' ? json.length : 'undefined',
                        isReady: typeof isReady !== 'undefined' ? isReady : 'undefined'
                    };
                """)
                print(f"JavaScript变量状态: {js_vars}")
                
            except Exception as e:
                print(f"页面状态检查失败: {e}")
            
            return False
            
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        return False
        
    finally:
        if driver:
            print("\n测试完成，5秒后关闭浏览器...")
            time.sleep(5)
            driver.quit()

def main():
    success = test_js_data_extraction()
    if success:
        print("\n🎉 JavaScript数据获取测试成功！")
    else:
        print("\n❌ JavaScript数据获取测试失败")

if __name__ == "__main__":
    main()

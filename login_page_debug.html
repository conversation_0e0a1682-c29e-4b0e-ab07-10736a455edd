<html><head>
    <meta charset="UTF-8">
    <title>高质量科技期刊及学术会议分级查询</title>
    <link rel="stylesheet" href="../layui/css/layui.css">
    <link rel="stylesheet" href="../layui/lay/modules/tableFilter/tableFilter.css">
    <link rel="stylesheet" href="../css/oplog.css">
    <link rel="stylesheet" type="text/css" href="/style/version4/login_v3.css">
    <link rel="stylesheet" href="/style/zebra_dialog.css" type="text/css">
    <script type="text/javascript" src="/js/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="/js/version4/jquery.SuperSlide.2.1.1.js"></script>
    <script type="text/javascript" src="/js/version/bound.js"></script>
    <script type="text/javascript" src="/js/zebra_dialog.js"></script>
    <script type="text/javascript" src="/style/excel/js/layer.js"></script><link rel="stylesheet" href="https://ir.scu.edu.cn/style/excel/js/theme/default/layer.css?v=3.1.1" id="layuicss-layer">
    <style>
        .book-list,.book-list>li{list-style: none;margin:0;padding:0;}
        .book-list>li span{cursor: pointer;}
        .book-list>li{width: 370px;display:block;padding:3px 0;position: relative;padding-left:25px;font-size:14px;}
        .book-list>li a{padding-right:0;float:right;}
        .book-list>li span:before{content:'';width:20px;height:20px;position: absolute;background: url("/style/excel/images/pdf.jpeg")  center center no-repeat;background-size:contain;position: absolute;left: 0;right: 0;}
        .modal-body{padding:20px;min-width:300px;        }
        .layui-btn {height: 28px;line-height: 28px;}
        .container .lw-search-wrap.small .search{height:44px;overflow: visible;}
        .container .lw-search-wrap.small .search .select-grade {float: left;position: relative;width: 150px;height: 30px;margin-right: 15px;margin-top: 10px;line-height: 30px;background: #ffffff;border: 1px solid #a9aab4;border-radius: 4px;vertical-align: middle;padding: 0 16px;box-sizing: border-box;cursor: pointer;}
        .container .lw-search-wrap.small .search .select-grade span {font-size: 14px !important;text-align: left !important;}
        .container .lw-search-wrap.small .search .select-grade img {position: absolute;right: 12px;top: 10px;width: 12px;height: 8px;transform: rotate(180deg);-webkit-transform: rotate(180deg);-moz-transform: rotate(180deg);-ms-transform: rotate(180deg);-o-transform: rotate(180deg);        }
        .container .lw-search-wrap.small .search .select-grade .pull-down-common {overflow: hidden;display: none;position: absolute;left: -1px;top: 26px;width: 150px;max-height: 200px;background: #ffffff;border: 1px solid #a9aab4;border-top: none;border-bottom-left-radius: 4px;border-bottom-right-radius: 4px;font-size: 14px;z-index: 100;transition: transform 1s ease-out;padding: 8px 0;box-sizing: border-box;}
        .container .lw-search-wrap.small .search .select-grade .pull-down-common li {padding-left: 24px;color: #b7bbbf;}
        .layui-table-view .layui-table th:nth-child(5), .layui-table-view .layui-table td:nth-child(5){background-color:#feeeed !important;}
        .fl { float: left; }
        .left .data{display: none}
        .loginBtn{margin-top: 40px;margin-right: 40px;position: absolute;right: 0;top: 0;}
        .loginBtn .login_new{width:80px;border:2px solid#5ab9c1;height:30px;line-height:30px;text-align:center;border-radius:8px;color:#333;cursor:pointer;}
        .loginBtn .logout{width:80px;background-color:#5ab9c1;border:2px solid#5ab9c1;height:30px;line-height:30px;text-align:center;border-radius:8px;color:#ffffff;cursor:pointer;}
        .lw-search-wrap h3,h5,h6{margin:30px auto; font-size:14px;cursor:pointer; text-align:center; }
        .lw-search-wrap h6 p{ text-indent:50px;text-align: left}
        body{
            min-width:1000px;
        }
        tr.warning td:nth-child(6),tr.warning td:nth-child(5){color: red;}
    </style>
<link id="layuicss-laydate" rel="stylesheet" href="https://ir.scu.edu.cn/style/excel/layui/css/modules/laydate/default/laydate.css?v=5.0.9" media="all"></head>
<body class="gray" onmousemove="HideMenu()" oncontextmenu="return false" ondragstart="return false" onselectstart="return false" onselect="document.selection.empty()" oncopy="document.selection.empty()" onbeforecopy="return false" onmouseup="document.selection.empty()">
<div class="loginBtn">
    
        
            <div class="login_new">登录</div>
        
        
    
</div>
    <div class="container">
        <div class="lw-search-wrap small">
            <h3 style="color:#5ab9c1 !important;font-size: 18px;">
                <a href="javaScript:void();" style="color:#5ab9c1 !important;">高质量科技期刊及学术会议分级查询</a>
            </h3>
            <h6 style="width: 60%">
               <p>为加快推进我校世界一流大学建设，引导发表高质量论文，包括发表在具有国际影响力的国内科技期刊、业界公认的国际顶级或重要科技期刊的论文，以及在国内外顶级学术会议上进行报告的论文。经学校学术委员会研究制定了《高质量科技期刊及学术会议分级参考方案（暂行）》。</p>

            </h6>

            <div class="search" style="position: relative; overflow: visible;">

                <div class="select-grade flag-edit" style="position: absolute; left:0;top:-50px;">
                    <span>修订版本</span>
                    <img src="../images/drop_down.png" alt="">
                    <div class="pull-down-common">
                        <ul class="grade-list2">
                            <li>2021年4月</li>
                        </ul>
                    </div>
                </div>

                <div class="select-grade flag-edit">
                    <span>检索类别</span>
                    <img src="../images/drop_down.png" alt="">
                    <div class="pull-down-common">
                        <ul class="grade-list">
                            <li>期刊全称</li>
                            <li>期刊简称</li>
                            <li>ISSN</li>
                            <li>分类检索</li>
                        </ul>
                    </div>
                </div>
                <input placeholder="请输入" class="input" id="searchArea" style="width:70%" autocomplete="off" disabled="disabled">
                <div class="searchBtn" onclick="search();">查找</div>
            </div>
            <h5><a href="./scheme.html" style="color:#5ab9c1 !important;">高质量科技期刊及学术会议分级参考方案（分级原则）</a></h5>
            <h5><a href="javaScript:showWaring()" style="color:#5ab9c1 !important;">预警期刊名单</a></h5>

        </div>

        <div class="con" style="display:none;">
            <div class="left">
                <ul class="data" style="display: block;">
                    <li class="cur">全部</li>
                    <li>综合性期刊</li>
                    <li>生物学</li>
                    <li>材料科学</li>
                    <li>地球科学</li>
                    <li>工程技术</li>
                    <li>化学</li>
                    <li>计算机科学</li>
                    <li>数学</li>
                    <li>物理与天体物理</li>
                    <li>心理学</li>
                    <li>医学</li>
                    <li>法学</li>
                    <li>管理学</li>
                    <li>环境科学与生态学</li>
                    <li>教育学</li>
                    <li>经济学</li>
                    <li>农林科学</li>
                    <li>人文科学</li>

                </ul>
            </div> 
            <div class="right" style="height:auto;width: 703px;margin-left:240px; float:left;">
			

			
                <!--       lay-data='{height:200,page: false,url:"/demo/table/user/" }'      url为数据接口       -->
                <table class="layui-table" id="main-table" lay-filter="dataTable" lay-data="{height:300,page: true}">
                </table><div class="layui-form layui-border-box layui-table-view" lay-filter="LAY-table-2" lay-id="main-table" style="width:973px; height:600px;"><div class="layui-table-box"><div class="layui-table-header"><table cellspacing="0" cellpadding="0" border="0" class="layui-table"><thead><tr><th data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0"><span>期刊全称</span></div></th><th data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1"><span>期刊简称</span></div></th><th data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2"><span>ISSN</span></div></th><th data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3"><span>学科大类</span></div></th><th data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4"><span>2021年4月版分级</span></div></th></tr></thead></table></div><div class="layui-table-body layui-table-main" style="height: 519px;"><table cellspacing="0" cellpadding="0" border="0" class="layui-table"><tbody><tr data-index="0"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NATURE</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">0028-0836</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">综合性期刊</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A</div></td></tr><tr data-index="1"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">SCIENCE</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">SCIENCE</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">0036-8075</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">综合性期刊</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A</div></td></tr><tr data-index="2"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">CELL</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">CELL</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">0092-8674</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">生物学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A</div></td></tr><tr data-index="3"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE COMMUNICATIONS</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT COMMUN</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">2041-1723</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">综合性期刊</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="4"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">SCIENCE ADVANCES</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">SCI ADV</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">2375-2548</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">综合性期刊</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="5"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">PROCEEDINGS OF THE NATIONAL ACADEMY OF SCIENCES OF THE UNITED STATES OF AMERICA</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">P NATL ACAD SCI USA</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">0027-8424</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">综合性期刊</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="6"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE IMMUNOLOGY</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT IMMUNOL</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1529-2908</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="7"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE MEDICINE</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT MED</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1078-8956</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="8"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE NEUROSCIENCE</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT NEUROSCI</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1097-6256</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="9"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS CANCER</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV CANCER</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1474-175X</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="10"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS CARDIOLOGY</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV CARDIOL</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1759-5002</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="11"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS CLINICAL ONCOLOGY</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV CLIN ONCOL</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1759-4774</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="12"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS DISEASE PRIMERS</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV DIS PRIMERS</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">2056-676X</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="13"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS DRUG DISCOVERY</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV DRUG DISCOV</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1474-1776</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="14"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS ENDOCRINOLOGY</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV ENDOCRINOL</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1759-5029</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="15"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS GASTROENTEROLOGY &amp; HEPATOLOGY</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV GASTRO HEPAT</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1759-5045</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="16"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS IMMUNOLOGY</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV IMMUNOL</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1474-1733</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="17"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS NEPHROLOGY</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV NEPHROL</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1759-5061</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="18"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS NEUROLOGY</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV NEUROL</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1759-4758</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr><tr data-index="19"><td data-field="期刊全称" data-key="2-0-0" class=""><div class="layui-table-cell laytable-cell-2-0-0">NATURE REVIEWS NEUROSCIENCE</div></td><td data-field="期刊简称" data-key="2-0-1" class=""><div class="layui-table-cell laytable-cell-2-0-1">NAT REV NEUROSCI</div></td><td data-field="issn" data-key="2-0-2" class=""><div class="layui-table-cell laytable-cell-2-0-2">1471-003X</div></td><td data-field="学科大类" data-key="2-0-3" class=""><div class="layui-table-cell laytable-cell-2-0-3">医学</div></td><td data-field="分级" data-key="2-0-4" class=""><div class="layui-table-cell laytable-cell-2-0-4">A-</div></td></tr></tbody></table></div></div><div class="layui-table-page"><div id="layui-table-page2"><div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1"><a href="javascript:;" class="layui-laypage-prev layui-disabled" data-page="0"><i class="layui-icon"></i></a><span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em>1</em></span><a href="javascript:;" data-page="2">2</a><a href="javascript:;" data-page="3">3</a><span class="layui-laypage-spr">…</span><a href="javascript:;" class="layui-laypage-last" title="尾页" data-page="460">460</a><a href="javascript:;" class="layui-laypage-next" data-page="2"><i class="layui-icon"></i></a><span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input">页<button type="button" class="layui-laypage-btn">确定</button></span><span class="layui-laypage-count">共 9184 条</span><span class="layui-laypage-limits"><select lay-ignore=""><option value="20" selected="">20 条/页</option><option value="50">50 条/页</option><option value="100">100 条/页</option></select></span></div></div></div><style>.laytable-cell-2-0-0{ width: 300px; }.laytable-cell-2-0-1{ width: 200px; }.laytable-cell-2-0-2{ width: 150px; }.laytable-cell-2-0-3{ width: 150px; }.laytable-cell-2-0-4{ width: 150px; }</style></div>
    
    
            </div>
        </div>
       
       
    </div>
    <!-- 弹框开始 -->
    <div class="bound" id="bound" style="display:none;"></div>
    <div class="loginBox" style="display: none;">
        <div class="lgb-left fl">
            <div class="lgb-til">个人用户登录</div>
            <ul class="lgbitem">
                <li>
                    <span class="lgbitemTil">用户名：</span>
                    <input name="userName" type="text" id="userName" class="lgbInput">
                </li>
                <li>
                    <span class="lgbitemTil">密&nbsp;码：</span>
                    <input type="password" name="password" id="password" class="lgbInput">
                </li>
                <li style="display:block">
                    <table>
                        <tbody><tr>
                            <td>
                                <span class="lgbitemTil">验证码：</span>
                                <input type="text" id="randomCode" class="lgbInput yzmInput">
                            </td>
                            <td>
                                <img id="validatecodeimg" alt="看不清？刷新" style="width: 70px;margin-left: 10px;" onclick="javascript:changeImg()" src="first.RandomCode">
                            </td>
                        </tr>
                    </tbody></table>
                </li>
                <li>
                    <span class="lgbitemTil">&nbsp;</span>
                    <input type="button" name="" class="lgbBut" id="loginbtn1" value="确 定">
                </li>
            </ul>
        </div>
        <div class="lgb-right fl">
            <div class="lgb-del">
                <img src="/images/version/lgdel.jpg">
            </div>
            <div class="lgb-sm">
                <div class="clearfix matt19">
                    <div class="lgb-num fl">1.</div>
                    <p class="lgbsm-tex fl">用户名为一卡通号，密码为图书馆借阅密码</p>
                </div>
                <div class="clearfix matt19">
                    <div class="lgb-num fl">2.</div>
                    <p class="lgbsm-tex fl">作为文献管理和团队科研协作系统，需用户补充基本信息</p>
                </div>
                <div class="clearfix matt19">
                    <div class="lgb-num fl">3.</div>
                    <p class="lgbsm-tex fl">初始密码是身份证号除X外的最后6个数字，或者是8位数生日</p>
                </div>
                <div class="clearfix matt19">
                    <div class="lgb-num fl"></div>
                    <p class="lgbsm-tex fl">联系电话：028-85404109</p>
                </div>
            </div>
        </div>
        <div class="clear"></div>
    </div>
    <div id="modalWaring" style="display: none">
        <div class="modal-body">
            <ul class="book-list">
                <li><span onclick="openFile(1);">2020年科睿唯安镇压期刊名单.pdf</span><a href="/publicUser/excel/downLoad?filename=2020年科睿唯安镇压期刊名单.pdf">下载</a></li>
                <li><span onclick="openFile(2);">2021年《国际期刊预警名单（试行）》.pdf</span><a href="/publicUser/excel/downLoad?filename=2021年《国际期刊预警名单（试行）》.pdf">下载</a></li>
                <li><span onclick="openFile(3);">2021年科睿唯安镇压期刊名单.pdf</span><a href="/publicUser/excel/downLoad?filename=2021年科睿唯安镇压期刊名单.pdf">下载</a></li>
                <li><span onclick="openFile(4);">2022年《国际期刊预警名单（试行）》.pdf</span><a href="/publicUser/excel/downLoad?filename=2022年《国际期刊预警名单（试行）》.pdf">下载</a></li>
            </ul>
        </div>
    </div>
    <!-- 弹框结束 -->

<script src="../layui/layui.js"></script>
<script src="../js/jquery.min.js" type="text/javascript" charset="utf-8"></script>
<script>
    var page2 = "/style/excel/views/index4.jsp";
    var dataUrl = "/style/d.json";
    (function (c) {
        c.Zebra_Dialog = function (g, p) {
            var u = {animation_speed_hide: 250, animation_speed_show: 0, auto_close: !1, buttons: !0, center_buttons: !1, custom_class: !1, keyboard: !0, max_height: 0, message: "", modal: !0, overlay_close: !0, overlay_opacity: ".9", position: "center", reposition_speed: 500, show_close_button: !0, source: !1, title: "", type: "information", vcenter_short_message: !0, width: 0, onClose: null}, a = this, k = {}, q;
            a.settings = {};
            "string" == typeof g && (k.message = g);
            if ("object" == typeof g || "object" == typeof p)k = c.extend(k, "object" == typeof g ? g : p);
            a.init = function () {
                var d;
                a.settings = c.extend({}, u, k);
                a.isIE6 = "explorer" == m.name && 6 == m.version || !1;
                a.settings.modal && (a.overlay = jQuery("<div>", {"class": "ZebraDialogOverlay"}).css({position: a.isIE6 ? "absolute" : "fixed", left: 0, top: 0, opacity: a.settings.overlay_opacity}), a.settings.overlay_close && a.overlay.bind("click", function () {
                    a.close()
                }), a.overlay.appendTo("body"));
                a.dialog = jQuery("<div>", {"class": "ZebraDialog" + (a.settings.custom_class ? " " + a.settings.custom_class : "")}).css({position: a.isIE6 ? "absolute" : "fixed", left: 0, top: 0, visibility: "hidden"});
                !a.settings.buttons && a.settings.auto_close && a.dialog.attr("id", "ZebraDialog_" + Math.floor(9999999 * Math.random()));
                var b = parseInt(a.settings.width, 10);
                !isNaN(b) && b == a.settings.width && b.toString() == a.settings.width.toString() && 0 < b && a.dialog.css({width: a.settings.width});
                a.settings.title && (d = jQuery("<h3>", {"class": "ZebraDialog_Title"}).html(a.settings.title).appendTo(a.dialog));
                if (!0 === a.settings.buttons || c.isArray(a.settings.buttons)) {
                    if (!0 === a.settings.buttons)switch (a.settings.type) {
                        case "question":
                            a.settings.buttons = ["确定", "取消"];
                            break;
                        default:
                            a.settings.buttons = ["确定"]
                    }
                    b = a.settings.buttons
                } else b = !1;
                var h = jQuery("<div>", {"class": "ZebraDialog_BodyOuter" + (a.settings.title ? "" : " ZebraDialog_NoTitle") + (b ? "" : " ZebraDialog_NoButtons")}).appendTo(a.dialog);
                a.message = jQuery("<div>", {"class": "ZebraDialog_Body" + ("" !== r() ? " ZebraDialog_Icon ZebraDialog_" + r() : "")});
                0 < a.settings.max_height && (a.message.css("max-height", a.settings.max_height), a.isIE6 && a.message.attr("style", "height: expression(this.scrollHeight > " + a.settings.max_height + ' ? "' + a.settings.max_height + 'px" : "85px")'));
                a.settings.vcenter_short_message ? jQuery("<div>").html(a.settings.message).appendTo(a.message) : a.message.html(a.settings.message);
                if (a.settings.source && "object" == typeof a.settings.source) {
                    var f = a.settings.vcenter_short_message ? c("div:first", a.message) : a.message, e;
                    for (e in a.settings.source)switch (e) {
                        case "ajax":
                            var l = "string" == typeof a.settings.source[e] ? {url: a.settings.source[e]} : a.settings.source[e], g = jQuery("<div>").attr("class", "ZebraDialog_Preloader").appendTo(f);
                            l.success = function (a) {
                                g.remove();
                                f.append(a);
                                n(!1)
                            };
                            c.ajax(l);
                            break;
                        case "iframe":
                            l = c.extend({width: "100%", height: "100%", marginheight: "0", marginwidth: "0", frameborder: "0"}, "string" == typeof a.settings.source[e] ? {src: a.settings.source[e]} : a.settings.source[e]);
                            f.append(jQuery("<iframe>").attr(l));
                            break;
                        case "inline":
                            f.append(a.settings.source[e])
                    }
                }
                a.message.appendTo(h);
                if (b) {
                    b.reverse();
                    var s = jQuery("<div>", {"class": "ZebraDialog_Buttons"}).appendTo(a.dialog);
                    c.each(b, function (b, d) {
                        var e = jQuery("<a>", {href: "javascript:void(0)", "class": "ZebraDialog_Button_" + b});
                        c.isPlainObject(d) ? e.html(d.caption) : e.html(d);
                        e.bind("click", function () {
                            var b = !0;
                            void 0 !== d.callback && (b = d.callback(a.dialog));
                            !1 !== b && a.close(void 0 !== d.caption ? d.caption : d)
                        });
                        e.appendTo(s)
                    });
                    s.wrap(c("<div>").addClass("ZebraDialog_ButtonsOuter" + (a.settings.center_buttons ? " ZebraDialog_Buttons_Centered" : "")))
                }
                a.dialog.appendTo("body");
                a.settings.show_close_button && (e = c('<a href="javascript:void(0)" class="ZebraDialog_Close">&times;</a>').bind("click",function (b) {
                    b.preventDefault();
                    a.close()
                }).appendTo(d ? d : a.message), d && e.css({right: parseInt(d.css("paddingRight"), 10), top: (parseInt(d.css("height"), 10) + parseInt(d.css("paddingTop"), 10) + parseInt(d.css("paddingBottom"), 10) - e.height()) / 2}));
                c(window).bind("resize.Zebra_Dialog", function () {
                    clearTimeout(q);
                    q = setTimeout(function () {
                        n()
                    }, 100)
                });
                a.settings.keyboard && c(document).bind("keyup.Zebra_Dialog", function (b) {
                    27 == b.which && a.close();
                    return!0
                });
                a.isIE6 && c(window).bind("scroll.Zebra_Dialog", function () {
                    t()
                });
                !1 !== a.settings.auto_close && (a.dialog.bind("click", function () {
                    clearTimeout(a.timeout);
                    a.close()
                }), a.timeout = setTimeout(a.close, a.settings.auto_close));
                n(!1);
                return a
            };
            a.close = function (d) {
                c(document).unbind(".Zebra_Dialog");
                c(window).unbind(".Zebra_Dialog");
                a.overlay && a.overlay.animate({opacity: 0}, a.settings.animation_speed_hide, function () {
                    a.overlay.remove()
                });
                a.dialog.animate({top: 0, opacity: 0}, a.settings.animation_speed_hide, function () {
                    a.dialog.remove();
                    if (a.settings.onClose && "function" == typeof a.settings.onClose)a.settings.onClose(void 0 !== d ? d : "")
                })
            };
            var n = function (d) {
                var b = c(window).width(), h = c(window).height(), f = a.dialog.width(), e = a.dialog.height(), b = {left: 0, top: 0, right: b - f, bottom: h - e, center: (b - f) / 2, middle: (h - e) / 2};
                a.dialog_left = void 0;
                a.dialog_top = void 0;
                c.isArray(a.settings.position) && 2 == a.settings.position.length && "string" == typeof a.settings.position[0] && a.settings.position[0].match(/^(left|right|center)[\s0-9\+\-]*$/) && "string" == typeof a.settings.position[1] && a.settings.position[1].match(/^(top|bottom|middle)[\s0-9\+\-]*$/) && (a.settings.position[0] = a.settings.position[0].toLowerCase(), a.settings.position[1] = a.settings.position[1].toLowerCase(), c.each(b, function (b, d) {
                    for (var c = 0; 2 > c; c++) {
                        var e = a.settings.position[c].replace(b, d);
                        e != a.settings.position[c] && (0 === c ? a.dialog_left = eval(e) : a.dialog_top = eval(e))
                    }
                }));
                if (void 0 === a.dialog_left || void 0 === a.dialog_top)a.dialog_left = b.center, a.dialog_top = b.middle;
                a.settings.vcenter_short_message && (b = a.message.find("div:first"), h = b.height(), f = a.message.height(), h < f && b.css({"padding-top": (f - h) / 2}));
                "boolean" == typeof d && !1 === d || 0 === a.settings.reposition_speed ? a.dialog.css({left: a.dialog_left, top: a.dialog_top, visibility: "visible", opacity: 0}).animate({opacity: 1}, a.settings.animation_speed_show) : (a.dialog.stop(!0), a.dialog.css("visibility", "visible").animate({left: a.dialog_left, top: a.dialog_top}, a.settings.reposition_speed));
                a.dialog.find("a[class^=ZebraDialog_Button]:first").focus();
                a.isIE6 && setTimeout(t, 500)
            }, t = function () {
                var d = c(window).scrollTop(), b = c(window).scrollLeft();
                a.settings.modal && a.overlay.css({top: d, left: b});
                a.dialog.css({left: a.dialog_left + b, top: a.dialog_top + d})
            }, r = function () {
                switch (a.settings.type) {
                    case "confirmation":
                    case "error":
                    case "information":
                    case "question":
                    case "warning":
                        return a.settings.type.charAt(0).toUpperCase() + a.settings.type.slice(1).toLowerCase();
                    default:
                        return!1
                }
            }, m = {init: function () {
                    this.name = this.searchString(this.dataBrowser) || "";
                    this.version = this.searchVersion(navigator.userAgent) || this.searchVersion(navigator.appVersion) || ""
                }, searchString: function (a) {
                    for (var b = 0; b < a.length; b++) {
                        var c = a[b].string, f = a[b].prop;
                        this.versionSearchString = a[b].versionSearch || a[b].identity;
                        if (c) {
                            if (-1 != c.indexOf(a[b].subString))return a[b].identity
                        } else if (f)return a[b].identity
                    }
                }, searchVersion: function (a) {
                    var b = a.indexOf(this.versionSearchString);
                    if (-1 != b)return parseFloat(a.substring(b + this.versionSearchString.length + 1))
                }, dataBrowser: [
                    {string: navigator.userAgent, subString: "MSIE", identity: "explorer", versionSearch: "MSIE"}
                ]};
            m.init();
            return a.init()
        }
    })(jQuery);
    $(".select-grade").on("click", function (e) {
        $(".select-grade").css('zIndex', 10)
        $(this).css('zIndex', 100);

        if ($(this).attr("isClick") == 0) {
            $(this)
                .find(".pull-down-common")
                .hide();
            $(this).attr("isClick", 1);
        } else {
            $('.select-grade').attr("isClick", 1);
            $('.pull-down-common').hide();

            $(this)
                .find(".pull-down-common")
                .show();
            $(this).attr("isClick", 0);
        }
        stopBubble(e);
    });
    $(".grade-list2 li").on("click", function () {
        // 选择具体项
        $(this)
            .parents(".select-grade")
            .find("span")
            .text($(this).text())
            .css("color", "#2b333b");

        $(this).css("color", "#2b333b").siblings('li').css('color', '#b7bbbf');
        $(this).css("color", "#2b333b").siblings('li').css('color', '#b7bbbf');

    });
    $(".grade-list li").on("click", function () {
        // 选择具体项
        $(this)
            .parents(".select-grade")
            .find("span")
            .text($(this).text())
            .css("color", "#2b333b");

        $(this).css("color", "#2b333b").siblings('li').css('color', '#b7bbbf');
        if($(this).text().indexOf("分类检索")<0){
            window.location.href=page2+"?index="+$(this).index();
        }
        $(this).css("color", "#2b333b").siblings('li').css('color', '#b7bbbf');

    });

    //阻止冒泡
    function stopBubble(e) {
        if(e && e.stopPropagation) {
            e.stopPropagation();
        } else {
            window.event.cancelBubble = true;
        }
    }
    $(".letter ul").on("click","li",function(){
        $(this).addClass("cur").siblings().removeClass("cur");
        search();
    })
    $(".container .con").on("click",".left ul.data li",function(){
        $(this).addClass("cur").siblings().removeClass("cur");
        search();
    })
    var json= [];
	var isReady = false;

    function load(){
        $.ajax({
            type:'get',
            url : '/style/d2.json',
            async: false,
            dataType : 'json',
            success : function(data) {
                json = data;
				isReady = true;
				search();
            }
        });
    }
  $(function(){
      load();
      $("#searchArea").attr("disabled","disabled")
      showLeftData()
      
      $("#loginbtn1").click(function() {
          var flag = true;
          var userName = $.trim($("#userName").val());
          var password = $.trim($("#password").val());
          var randomCode = $.trim($("#randomCode").val());
          var obj = {};
          obj.userId = "";
          obj.userName = userName;
          obj.password = password;
          obj.randomCode = randomCode;
          obj.unitId = "1";
          obj.type = 2;

          if (userName == null || userName == "") {
          flag = false;
          $.Zebra_Dialog('用户名不能为空!', {
              'type' : 'information',
              'title' : '机构库登录'
          });
          return;
      }
      if ("" == password) {
          flag = false;
          $.Zebra_Dialog('密码不能为空!', {
              'type' : 'information',
              'title' : '机构库登录'
          });
          return;
      }else {
          if (flag) {
              $.ajax({
                  type : "post",
                  dataType : "json",
                  url : "/publicUser/testLogin",
                  data : obj,
                  success : function(data, textStatus) {
                      var msg = data.stauts;
                      var info = data.info;
                      if ("1001" == msg) {
                          $.Zebra_Dialog(info, {
                              'type' : 'information',
                              'title' : '机构库登录'
                          });
                      } else if ("1007" == msg) {
                          $.Zebra_Dialog('用户已在别处登录或登录下关闭浏览器，登录吗？', {
                              'type' : 'confirmation',
                              'title' : '机构库用户登录',
                              'buttons' : [
                                  {
                                      caption : '确定',
                                      callback : function() {
                                          $.ajax({
                                              type : "post",
                                              dataType : "json",
                                              url : "/publicUser/oCSLogin",
                                              data : obj,
                                              success : function(data, textStatus) {
                                                  var msg = data.stauts;
                                                  var info = data.info;
                                                  if ("1001" == msg) {
                                                      $.Zebra_Dialog('用户名密码输入有误', {
                                                          'type' : 'information',
                                                          'title' : '机构库登录'
                                                      });
                                                  } else if ("1002" == msg) {
                                                      $.Zebra_Dialog('用户名时间失效', {
                                                          'type' : 'information',
                                                          'title' : '机构库登录'
                                                      });
                                                  } else if ("1003" == msg) {
                                                      $.Zebra_Dialog('该用户超过登录机器数', {
                                                          'type' : 'information',
                                                          'title' : '机构库登录'
                                                      });
                                                  } else if ("1004" == msg) {
                                                      $.Zebra_Dialog(info, {
                                                          'type' : 'information',
                                                          'title' : '机构库登录'
                                                      });
                                                  } else if ("1005" == msg) {
                                                      $.Zebra_Dialog('验证码错误', {
                                                          'type' : 'information',
                                                          'title' : '机构库登录'
                                                      });
                                                      $("#randomCode").val("");
                                                      changeImg();
                                                  } else if ("1006" == msg) {
                                                      $.Zebra_Dialog('登录出错', {
                                                          'type' : 'information',
                                                          'title' : '机构库登录'
                                                      });
                                                  } else if ("1008" == msg) {
                                                      $.Zebra_Dialog('该用户已注销', {
                                                          'type' : 'information',
                                                          'title' : '机构库登录'
                                                      });
                                                  } else if ("1000" == msg) {
                                                      if ("1111" == data.pass_stauts) {
                                                          // 密码强度提示
                                                          showModify();
                                                      } else {
                                                          window.location.reload();
                                                      }
                                                  }
                                              }
                                          });
                                      }
                                  }, {
                                      caption : '取消',
                                      callback : function() {
                                      }
                                  }
                              ]
                          });
                      } else {
                          $.ajax({
                              type : "post",
                              dataType : "json",
                              url : "/publicUser/oCSLogin",
                              data : obj,
                              success : function(data, textStatus) {
                                  var msg = data.stauts;
                                  var info = data.info;
                                  if ("1001" == msg) {
                                      $.Zebra_Dialog('用户名密码输入有误', {
                                          'type' : 'information',
                                          'title' : '机构库登录'
                                      });
                                  } else if ("1002" == msg) {
                                      $.Zebra_Dialog('用户名时间失效', {
                                          'type' : 'information',
                                          'title' : '机构库登录'
                                      });
                                  } else if ("1003" == msg) {
                                      $.Zebra_Dialog('该用户超过登录机器数', {
                                          'type' : 'information',
                                          'title' : '机构库登录'
                                      });
                                  } else if ("1004" == msg) {
                                      $.Zebra_Dialog(info, {
                                          'type' : 'information',
                                          'title' : '机构库登录'
                                      });
                                  } else if ("1005" == msg) {
                                      $.Zebra_Dialog('验证码错误', {
                                          'type' : 'information',
                                          'title' : '机构库登录'
                                      });
                                      $("#randomCode").val("");
                                      changeImg();
                                  } else if ("1006" == msg) {
                                      $.Zebra_Dialog('登录出错', {
                                          'type' : 'information',
                                          'title' : '机构库登录'
                                      });
                                  } else if ("1008" == msg) {
                                      $.Zebra_Dialog('该用户已注销', {
                                          'type' : 'information',
                                          'title' : '机构库登录'
                                      });
                                  }else if ("1010" == msg) {
                                      $.Zebra_Dialog('账号失败次数已达每日上限', {
                                          'type' : 'information',
                                          'title' : '机构库登录'
                                      });
                                  } else if ("1000" == msg) {
                                      if ("1111" == data.pass_stauts) {
                                          // 密码强度提示
                                          showModify();
                                      } else {
                                          window.location.reload();
                                      }
                                  }
                              }
                          });
                      }
                  }
              });
          }
      }
  })
      $("#update").click(function() {
          $("#userName").val("");
          $("#password").val("");
      })
  })
    function showModify() {
        layer.open({
            type : 2,
            title : false,
            closeBtn : [
                0, false
            ],
            scrollbar : false,
            maxmin : false,
            shadeClose : true, //开启点击遮罩关闭层
            area : [
                '625px', '325px'
            ],
            border : [
                0
            ],
            content : [
                "/publicUser/modifyPasswordPBS"
            ],
            end : function() {
                layer.closeAll();
                $("#validatecodeimg").click();
            }
        });
    }
    function showLeftData() {
        if (!isReady) {
            return false;
        }
        $(".left .data").show();
    }
    function toLogin(){
        $.ajax({
            type : "post",
            dataType : "text/html",
            url : "/style/excel/addTag.jsp",
            success : function() {
                window.location.href="https://id.scu.edu.cn/enduser/sp/logout/scdxplugin_jwt65?enterpriseId=scdx&force=true";
            },
            error:function (){
                window.location.href="https://id.scu.edu.cn/enduser/sp/logout/scdxplugin_jwt65?enterpriseId=scdx&force=true";
            }
        });
    }
    $(".login_new").click(function(){
        toLogin();
    });
    function search(){
		if(!isReady){
			return false;
		}
		$(".right h3").hide();
        var keyword = $("#searchArea").val();
        keyword = keyword==undefined ? "" : keyword;
        var clazz = $(".left ul.data li.cur").html();
        var l = 0;
        var count = 0;
        var list = [];
        for (var i in json){
            var info = json[i];
            //检索
            if ($(".select-grade span").html()=="期刊简称"){
                if (keyword!="" && info.期刊简称.toUpperCase()!=keyword.toUpperCase()){
                    continue;
                }
            }else if($(".select-grade span").html()=="ISSN"){
                if (keyword!="" && info.issn.toUpperCase()!=keyword.toUpperCase()){
                    continue;
                }
            }else if($(".select-grade span").html()=="分类"){
                if (keyword!="" && info.分级!=keyword.trim()){
                    continue;
                }
            }else {
                if (keyword!="" && info.期刊全称.toUpperCase()!=keyword.toUpperCase()){
                    continue;
                }
            }
            //大类
            if (clazz=="未纳入"){
                if(info.学科大类!='/'){
                    continue;
                }
            }else if (clazz!="全部" && info.学科大类!=clazz){
                continue;
            }
            info.期刊全称 = info.期刊全称.toUpperCase();
            list[l++] = info
        }

        init(list);

    }
    function changeImg() {
        $("#validatecodeimg").attr("src", Math.round(Math.random() * 10000) + ".RandomCode");
    }
    function init(data){
        layui.use(['table', 'laydate'], function () {
            var table = layui.table;
            table.render({
                elem: '#main-table'
                ,data:data
                ,width: 973
                ,height: 600
                ,cols: [[
                    {field:'期刊全称', width:300, title: '期刊全称'}
                    ,{field:'期刊简称', width:200, title: '期刊简称'}
                    ,{field:'issn', width:150, title: 'ISSN'}
                    ,{field:'学科大类', width:150, title: '学科大类'}
                    ,{field:'分级', width:150, title: '2021年4月版分级'}
                ]]
                ,page: true
                ,limits: [20,50,100]
                ,limit: 20

            });
        });
    }
    function logout(){
        $.ajax({
            type : "post",
            url : "/publicUser/loginOneCardOut",
            success : function(data, textStatus) {
                toLogin();
            },
            error:function (){
                toLogin();
            },
            timeout:function (){
                toLogin();
            }
        });
    }
    function showWaring(){
        layer.open({
            type: 1,
            title: false,
            closeBtn: true,
            shadeClose: true,
            isOutAnim: true,
            move: '.modal1 .title',
            content:$('#modalWaring'),
            area: ['auto', 'auto'],
            success: function () {

            },
        });
    }
    function openFile(id){
        var url = "/style/excel/doc/";
        var fileName = "2020年科睿唯安镇压期刊名单.pdf";
        switch (id){
            case 2:
                fileName = "2021年《国际期刊预警名单（试行）》.pdf";
                break;
            case 3:
                fileName = "2021年科睿唯安镇压期刊名单.pdf";
                break;
            case 4:
                fileName = "2022年《国际期刊预警名单（试行）》.pdf";
                break;
        }
        window.open(url+fileName,"_blank","")
    }
</script>

</body></html>
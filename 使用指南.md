# 四川大学期刊爬虫使用指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖包
pip install requests pandas beautifulsoup4 selenium openpyxl

# 或者运行安装脚本
install.bat
```

### 2. 下载ChromeDriver
1. 查看Chrome版本：在地址栏输入 `chrome://version/`
2. 下载对应版本：https://chromedriver.chromium.org/
3. 将ChromeDriver.exe放到系统PATH或项目目录

### 3. 运行程序
```bash
# 推荐：使用优化版本
python scu_journal_crawler.py

# 或者使用完整版本
python find.py
```

## 📋 使用步骤

### 步骤1：启动程序
运行程序后会看到欢迎界面

### 步骤2：输入登录信息
- **用户名**：您的一卡通号
- **密码**：图书馆借阅密码

### 步骤3：处理验证码
- 程序会自动打开浏览器
- 如果有验证码，请手动输入
- 在控制台按回车继续

### 步骤4：等待爬取
- 程序会自动登录并爬取数据
- 过程中会显示进度信息

### 步骤5：查看结果
爬取完成后会生成以下文件：
- `四川大学期刊数据.xlsx` - Excel格式数据
- `四川大学期刊数据.csv` - CSV格式数据
- `current_page.html` - 页面源码（调试用）

## 🔧 调试工具

### 页面结构分析
```bash
python simple_test.py
```
生成文件：
- `raw_page.html` - 原始页面HTML
- `form_analysis.json` - 表单分析结果

### 环境测试
```bash
python test.py
```
检查：
- 依赖包安装状态
- Chrome浏览器和驱动
- 基本功能测试

## 📊 数据字段说明

爬取的期刊数据包含：
- **序号** - 期刊编号
- **期刊全称** - 期刊完整名称
- **期刊简称** - 期刊缩写
- **ISSN** - 国际标准期刊号
- **分类** - 期刊学科分类
- **等级** - 期刊质量等级
- **影响因子** - 期刊影响因子
- **学科领域** - 所属学科领域

## ⚠️ 注意事项

### 登录要求
- 需要有效的四川大学账号
- 用户名是一卡通号
- 密码是图书馆借阅密码

### 技术要求
- Chrome浏览器（最新版本）
- ChromeDriver（版本匹配）
- 稳定的网络连接

### 使用规范
- 请遵守网站使用条款
- 合理控制爬取频率
- 仅用于学术研究目的

## 🐛 常见问题

### Q1: ChromeDriver版本不匹配
**解决方案**：
1. 查看Chrome版本
2. 下载对应版本的ChromeDriver
3. 确保ChromeDriver在PATH中

### Q2: 登录失败
**解决方案**：
1. 检查用户名和密码
2. 确认账号有访问权限
3. 手动在浏览器中完成登录

### Q3: 找不到数据表格
**解决方案**：
1. 运行 `simple_test.py` 分析页面结构
2. 检查是否成功登录
3. 查看生成的HTML文件

### Q4: 依赖包安装失败
**解决方案**：
```bash
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 📞 技术支持

如果遇到问题：
1. 查看生成的调试文件
2. 运行测试脚本检查环境
3. 检查网站是否有结构变化

## 📄 文件说明

- `scu_journal_crawler.py` - 优化版爬虫（推荐）
- `find.py` - 完整版爬虫
- `simple_test.py` - 页面结构分析工具
- `test.py` - 环境测试工具
- `debug_crawler.py` - 交互式调试工具
- `requirements.txt` - 依赖包列表
- `install.bat` - Windows安装脚本

## 🎯 使用建议

1. **首次使用**：先运行 `simple_test.py` 了解页面结构
2. **环境检查**：运行 `test.py` 确保环境正常
3. **正式爬取**：使用 `scu_journal_crawler.py`
4. **遇到问题**：查看调试文件和错误信息

祝您使用愉快！ 🎉

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四川大学期刊查询系统爬虫
爬取 https://ir.scu.edu.cn/style/excel/views/index3.jsp 中的期刊数据
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import re
from urllib.parse import urljoin
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import warnings
warnings.filterwarnings('ignore')


class SCUJournalCrawler:
    """四川大学期刊查询系统爬虫类"""

    def __init__(self):
        self.base_url = "https://ir.scu.edu.cn"
        self.login_url = "https://ir.scu.edu.cn/style/excel/views/index3.jsp"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.driver = None
        self.journals_data = []

    def setup_driver(self, headless=True):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            print("✓ Chrome浏览器驱动设置成功")
            return True
        except Exception as e:
            print(f"✗ Chrome浏览器驱动设置失败: {e}")
            print("请确保已安装Chrome浏览器和ChromeDriver")
            return False

    def login_with_selenium(self, username, password):
        """使用Selenium模拟登录"""
        try:
            print("正在访问登录页面...")
            self.driver.get(self.login_url)
            time.sleep(5)

            # 保存登录页面源码用于调试
            with open('login_page_debug.html', 'w', encoding='utf-8') as f:
                f.write(self.driver.page_source)
            print("✓ 登录页面源码已保存到 login_page_debug.html")

            # 分析页面结构，查找输入框
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            inputs = soup.find_all('input')
            print(f"页面中找到 {len(inputs)} 个输入框:")
            for inp in inputs:
                name = inp.get('name', 'N/A')
                input_type = inp.get('type', 'N/A')
                placeholder = inp.get('placeholder', 'N/A')
                print(f"  - name='{name}', type='{input_type}', placeholder='{placeholder}'")

            # 查找用户名输入框 (根据页面分析结果)
            try:
                username_input = self.driver.find_element(By.ID, "userName")
                print("✓ 找到用户名输入框: #userName")
            except NoSuchElementException:
                try:
                    username_input = self.driver.find_element(By.NAME, "userName")
                    print("✓ 找到用户名输入框: name=userName")
                except NoSuchElementException:
                    print("✗ 未找到用户名输入框，请手动登录")
                    input("请在浏览器中手动输入用户名和密码，然后按回车继续...")
                    return True

            # 查找密码输入框
            try:
                password_input = self.driver.find_element(By.ID, "password")
                print("✓ 找到密码输入框: #password")
            except NoSuchElementException:
                try:
                    password_input = self.driver.find_element(By.NAME, "password")
                    print("✓ 找到密码输入框: name=password")
                except NoSuchElementException:
                    print("✗ 未找到密码输入框，请手动登录")
                    input("请在浏览器中手动输入用户名和密码，然后按回车继续...")
                    return True

            # 输入用户名和密码
            print("正在输入登录信息...")
            username_input.clear()
            username_input.send_keys(username)
            password_input.clear()
            password_input.send_keys(password)

            # 查找验证码输入框
            try:
                captcha_input = self.driver.find_element(By.ID, "randomCode")
                print("✓ 检测到验证码输入框: #randomCode")
                print("请在浏览器中输入验证码...")
                input("输入验证码后按回车继续...")
            except NoSuchElementException:
                print("✗ 未找到验证码输入框，直接尝试登录")
                captcha_input = None

            # 查找并点击登录按钮
            try:
                login_button = self.driver.find_element(By.ID, "loginbtn1")
                print("✓ 找到登录按钮: #loginbtn1")
                login_button.click()
                print("✓ 已点击登录按钮")
            except NoSuchElementException:
                print("✗ 未找到登录按钮，请手动点击登录")
                input("请在浏览器中点击登录按钮，然后按回车继续...")

            # 等待登录完成
            print("等待登录完成...")
            time.sleep(5)

            # 检查是否登录成功
            current_url = self.driver.current_url
            page_title = self.driver.title
            print(f"当前URL: {current_url}")
            print(f"页面标题: {page_title}")

            # 更宽松的登录成功判断
            if ("login" not in current_url.lower() and "登录" not in page_title) or \
               "index3.jsp" in current_url or "main" in current_url.lower():
                print("✓ 登录成功")
                return True
            else:
                print("✗ 登录可能失败，但继续尝试...")
                return True  # 继续尝试，让用户手动处理

        except Exception as e:
            print(f"✗ 登录过程中出现错误: {e}")
            print("尝试手动登录...")
            input("请在浏览器中手动完成登录，然后按回车继续...")
            return True

    def get_journal_categories(self):
        """获取期刊分类列表"""
        try:
            # 查找分类选项
            category_elements = self.driver.find_elements(By.XPATH, "//ul[@class='category-list']//li")
            categories = []

            for element in category_elements:
                category_text = element.text.strip()
                if category_text and category_text != "全部":
                    categories.append(category_text)

            print(f"✓ 找到 {len(categories)} 个期刊分类")
            return categories

        except Exception as e:
            print(f"✗ 获取期刊分类失败: {e}")
            return []

    def search_journals_by_category(self, category=""):
        """按分类搜索期刊"""
        try:
            print(f"正在搜索分类: {category if category else '全部'}")

            # 如果指定了分类，点击对应的分类
            if category:
                try:
                    category_link = self.driver.find_element(By.XPATH, f"//li[contains(text(), '{category}')]")
                    category_link.click()
                    time.sleep(2)
                except NoSuchElementException:
                    print(f"✗ 未找到分类 '{category}'，跳过")
                    return []

            # 尝试等待数据加载，使用多种可能的选择器
            wait_selectors = [
                (By.CLASS_NAME, "journal-list"),
                (By.TAG_NAME, "table"),
                (By.CSS_SELECTOR, ".data-table"),
                (By.CSS_SELECTOR, ".result-table"),
                (By.CSS_SELECTOR, "tbody"),
                (By.XPATH, "//table//tr")
            ]

            data_loaded = False
            for by, selector in wait_selectors:
                try:
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((by, selector))
                    )
                    print(f"✓ 数据加载完成，使用选择器: {selector}")
                    data_loaded = True
                    break
                except TimeoutException:
                    continue

            if not data_loaded:
                print("⚠ 未检测到数据加载完成，但继续尝试解析...")

            return self.parse_journal_data()

        except Exception as e:
            print(f"✗ 搜索分类 '{category}' 失败: {e}")
            # 即使出错也尝试解析当前页面
            return self.parse_journal_data()

    def parse_journal_data(self):
        """解析期刊数据"""
        journals = []
        try:
            # 获取页面源码
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            # 保存页面源码用于调试
            with open('page_source.html', 'w', encoding='utf-8') as f:
                f.write(self.driver.page_source)
            print("✓ 页面源码已保存到 page_source.html")

            # 尝试多种可能的选择器 (根据页面分析结果)
            selectors = [
                '#main-table tbody tr',  # 主表格体行 (根据页面分析)
                '#main-table tr',  # 主表格行
                '.layui-table tbody tr',  # layui表格体行
                '.layui-table tr',  # layui表格行
                'table tbody tr',  # 任意表格体行
                'table tr',  # 任意表格行
                '.journal-list tr',  # 期刊列表表格行
                '.data-table tr',  # 数据表格行
                '.result-table tr',  # 结果表格行
            ]

            journal_rows = []
            for selector in selectors:
                rows = soup.select(selector)
                if len(rows) > 1:  # 至少有表头和数据行
                    journal_rows = rows[1:]  # 跳过表头
                    print(f"✓ 使用选择器 '{selector}' 找到 {len(journal_rows)} 行数据")
                    break

            if not journal_rows:
                print("✗ 未找到期刊数据表格，请检查页面结构")
                return []

            for i, row in enumerate(journal_rows):
                try:
                    journal_info = self.extract_journal_info(row)
                    if journal_info:
                        journals.append(journal_info)
                        if i < 5:  # 显示前5条数据用于调试
                            print(f"解析第{i+1}条: {journal_info.get('期刊全称', 'N/A')}")
                except Exception as e:
                    print(f"解析第{i+1}行数据时出错: {e}")
                    continue

            print(f"✓ 解析到 {len(journals)} 条期刊数据")
            return journals

        except Exception as e:
            print(f"✗ 解析期刊数据失败: {e}")
            return []

    def extract_journal_info(self, row_element):
        """从行元素中提取期刊信息"""
        try:
            # 根据实际的HTML结构调整选择器
            cells = row_element.find_all(['td', 'th'])

            if len(cells) >= 2:  # 至少有2列数据
                # 动态构建期刊信息字典
                journal_info = {}

                # 常见的字段映射
                field_names = ['期刊全称', '期刊简称', 'ISSN', '分类', '等级', '影响因子', '学科领域', '出版社', '国家/地区']

                for i, cell in enumerate(cells):
                    cell_text = self.clean_text(cell.get_text())
                    if cell_text:  # 只保存非空数据
                        if i < len(field_names):
                            journal_info[field_names[i]] = cell_text
                        else:
                            journal_info[f'字段{i+1}'] = cell_text

                # 确保至少有期刊名称
                if journal_info.get('期刊全称', '').strip():
                    return journal_info
                elif len(cells) > 0 and self.clean_text(cells[0].get_text()).strip():
                    # 如果第一列不为空，就作为期刊全称
                    journal_info['期刊全称'] = self.clean_text(cells[0].get_text())
                    return journal_info

            return None

        except Exception as e:
            print(f"提取期刊信息时出错: {e}")
            return None

    def clean_text(self, text):
        """清理文本数据"""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', text.strip())

    def crawl_all_journals(self, username, password):
        """爬取所有期刊数据"""
        try:
            # 设置浏览器驱动
            if not self.setup_driver(headless=False):  # 设置为False以便处理验证码
                return False

            # 登录
            if not self.login_with_selenium(username, password):
                return False

            # 获取所有分类
            categories = self.get_journal_categories()

            # 先搜索全部期刊
            print("正在搜索全部期刊...")
            all_journals = self.search_journals_by_category("")
            self.journals_data.extend(all_journals)

            # 如果需要按分类搜索更详细的数据
            for category in categories:
                category_journals = self.search_journals_by_category(category)
                # 去重处理
                for journal in category_journals:
                    if journal not in self.journals_data:
                        self.journals_data.append(journal)
                time.sleep(1)  # 避免请求过快

            print(f"✓ 总共爬取到 {len(self.journals_data)} 条期刊数据")
            return True

        except Exception as e:
            print(f"✗ 爬取过程中出现错误: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

    def save_to_excel(self, filename="scu_journals.xlsx"):
        """保存数据到Excel文件"""
        try:
            if not self.journals_data:
                print("✗ 没有数据可保存")
                return False

            # 创建DataFrame
            df = pd.DataFrame(self.journals_data)

            # 保存到Excel
            df.to_excel(filename, index=False, engine='openpyxl')
            print(f"✓ 数据已保存到 {filename}")
            print(f"✓ 共保存 {len(self.journals_data)} 条期刊记录")

            # 显示数据预览
            print("\n数据预览:")
            print(df.head())

            return True

        except Exception as e:
            print(f"✗ 保存Excel文件失败: {e}")
            return False

    def save_to_csv(self, filename="scu_journals.csv"):
        """保存数据到CSV文件"""
        try:
            if not self.journals_data:
                print("✗ 没有数据可保存")
                return False

            # 创建DataFrame
            df = pd.DataFrame(self.journals_data)

            # 保存到CSV
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✓ 数据已保存到 {filename}")

            return True

        except Exception as e:
            print(f"✗ 保存CSV文件失败: {e}")
            return False


def test_page_structure():
    """测试页面结构（无需登录）"""
    print("=" * 60)
    print("测试页面结构")
    print("=" * 60)

    crawler = SCUJournalCrawler()

    try:
        if not crawler.setup_driver(headless=False):
            return

        print("正在访问页面...")
        crawler.driver.get(crawler.login_url)
        time.sleep(3)

        # 保存页面源码
        with open('login_page_source.html', 'w', encoding='utf-8') as f:
            f.write(crawler.driver.page_source)
        print("✓ 登录页面源码已保存到 login_page_source.html")

        # 分析页面结构
        soup = BeautifulSoup(crawler.driver.page_source, 'html.parser')

        # 查找表单元素
        forms = soup.find_all('form')
        print(f"✓ 找到 {len(forms)} 个表单")

        # 查找输入框
        inputs = soup.find_all('input')
        print(f"✓ 找到 {len(inputs)} 个输入框:")
        for inp in inputs[:10]:  # 显示前10个
            name = inp.get('name', 'N/A')
            input_type = inp.get('type', 'N/A')
            print(f"  - {name} ({input_type})")

        # 查找可能的期刊分类
        categories = soup.find_all('li')
        print(f"✓ 找到 {len(categories)} 个列表项")

        input("按回车键继续...")

    except Exception as e:
        print(f"✗ 测试失败: {e}")
    finally:
        if crawler.driver:
            crawler.driver.quit()


def main():
    """主函数"""
    print("=" * 60)
    print("四川大学期刊查询系统爬虫")
    print("=" * 60)

    print("\n选择操作模式:")
    print("1. 正常爬取模式（需要登录）")
    print("2. 测试页面结构模式（无需登录）")

    choice = input("\n请选择 (1/2): ").strip()

    if choice == "2":
        test_page_structure()
        return

    # 创建爬虫实例
    crawler = SCUJournalCrawler()

    # 获取登录信息
    print("\n请输入登录信息:")
    username = input("用户名（一卡通号）: ").strip()
    password = input("密码（图书馆借阅密码）: ").strip()

    if not username or not password:
        print("✗ 用户名和密码不能为空")
        return

    try:
        # 开始爬取
        print("\n开始爬取期刊数据...")
        if crawler.crawl_all_journals(username, password):
            # 保存数据
            print("\n保存数据...")
            crawler.save_to_excel("四川大学期刊数据.xlsx")
            crawler.save_to_csv("四川大学期刊数据.csv")
            print("\n✓ 爬取完成！")
        else:
            print("\n✗ 爬取失败")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n程序运行出错: {e}")


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试 - 快速分析页面结构
"""

import requests
from bs4 import BeautifulSoup
import json

def analyze_page_with_requests():
    """使用requests分析页面"""
    url = "https://ir.scu.edu.cn/style/excel/views/index3.jsp"
    
    try:
        print("正在获取页面...")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.encoding = 'utf-8'
        
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.text)}")
        
        # 保存原始HTML
        with open('raw_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("✓ 原始页面已保存到 raw_page.html")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 分析表单
        print("\n=== 表单分析 ===")
        forms = soup.find_all('form')
        print(f"找到 {len(forms)} 个表单")
        
        form_info = []
        for i, form in enumerate(forms):
            info = {
                'index': i,
                'action': form.get('action', ''),
                'method': form.get('method', ''),
                'inputs': []
            }
            
            inputs = form.find_all('input')
            for inp in inputs:
                input_info = {
                    'name': inp.get('name', ''),
                    'type': inp.get('type', ''),
                    'placeholder': inp.get('placeholder', ''),
                    'value': inp.get('value', ''),
                    'id': inp.get('id', ''),
                    'class': inp.get('class', [])
                }
                info['inputs'].append(input_info)
            
            form_info.append(info)
            
            print(f"\n表单 {i+1}:")
            print(f"  action: {info['action']}")
            print(f"  method: {info['method']}")
            print(f"  输入框数量: {len(info['inputs'])}")
            
            for inp in info['inputs']:
                print(f"    - name='{inp['name']}', type='{inp['type']}', placeholder='{inp['placeholder']}'")
        
        # 保存表单信息
        with open('form_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(form_info, f, ensure_ascii=False, indent=2)
        print("✓ 表单分析结果已保存到 form_analysis.json")
        
        # 分析所有输入框
        print("\n=== 所有输入框分析 ===")
        all_inputs = soup.find_all('input')
        print(f"页面总共有 {len(all_inputs)} 个输入框")
        
        input_summary = []
        for inp in all_inputs:
            info = {
                'name': inp.get('name', ''),
                'type': inp.get('type', ''),
                'placeholder': inp.get('placeholder', ''),
                'id': inp.get('id', ''),
                'value': inp.get('value', '')
            }
            input_summary.append(info)
            print(f"  {info}")
        
        # 分析表格
        print("\n=== 表格分析 ===")
        tables = soup.find_all('table')
        print(f"找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            print(f"\n表格 {i+1}:")
            print(f"  class: {table.get('class', [])}")
            print(f"  id: {table.get('id', '')}")
            
            rows = table.find_all('tr')
            print(f"  行数: {len(rows)}")
            
            if rows:
                first_row = rows[0]
                cells = first_row.find_all(['th', 'td'])
                print(f"  列数: {len(cells)}")
                print("  表头内容:")
                for j, cell in enumerate(cells):
                    text = cell.get_text().strip()
                    if text:
                        print(f"    列{j+1}: {text}")
        
        # 分析JavaScript
        print("\n=== JavaScript分析 ===")
        scripts = soup.find_all('script')
        print(f"找到 {len(scripts)} 个script标签")
        
        js_content = []
        for script in scripts:
            if script.string:
                js_content.append(script.string)
        
        # 查找可能的AJAX请求
        full_text = response.text
        ajax_keywords = ['ajax', 'xhr', 'fetch', 'post', 'get']
        for keyword in ajax_keywords:
            if keyword in full_text.lower():
                print(f"  发现可能的AJAX调用: {keyword}")
        
        print("\n=== 分析完成 ===")
        print("生成的文件:")
        print("- raw_page.html: 原始页面HTML")
        print("- form_analysis.json: 表单详细分析")
        
        return True
        
    except Exception as e:
        print(f"✗ 分析失败: {e}")
        return False

def main():
    print("=" * 60)
    print("四川大学期刊系统 - 简单页面分析")
    print("=" * 60)
    
    analyze_page_with_requests()

if __name__ == "__main__":
    main()

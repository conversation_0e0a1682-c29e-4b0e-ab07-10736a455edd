#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据解析功能
"""

from bs4 import BeautifulSoup
import re

def clean_text(text):
    """清理文本"""
    if not text:
        return ""
    return re.sub(r'\s+', ' ', text.strip())

def test_parse_html():
    """测试解析HTML数据"""
    # 您提供的实际HTML数据
    html_data = '''
    <tbody>
        <tr data-index="0" class="">
            <td data-field="期刊全称" data-key="9-0-0" class="">
                <div class="layui-table-cell laytable-cell-9-0-0">NATURE</div>
            </td>
            <td data-field="期刊简称" data-key="9-0-1" class="">
                <div class="layui-table-cell laytable-cell-9-0-1">NATURE</div>
            </td>
            <td data-field="issn" data-key="9-0-2" class="">
                <div class="layui-table-cell laytable-cell-9-0-2">0028-0836</div>
            </td>
            <td data-field="学科大类" data-key="9-0-3" class="">
                <div class="layui-table-cell laytable-cell-9-0-3">综合性期刊</div>
            </td>
            <td data-field="分级" data-key="9-0-4" class="">
                <div class="layui-table-cell laytable-cell-9-0-4">A</div>
            </td>
        </tr>
        <tr data-index="1" class="">
            <td data-field="期刊全称" data-key="9-0-0" class="">
                <div class="layui-table-cell laytable-cell-9-0-0">SCIENCE</div>
            </td>
            <td data-field="期刊简称" data-key="9-0-1" class="">
                <div class="layui-table-cell laytable-cell-9-0-1">SCIENCE</div>
            </td>
            <td data-field="issn" data-key="9-0-2" class="">
                <div class="layui-table-cell laytable-cell-9-0-2">0036-8075</div>
            </td>
            <td data-field="学科大类" data-key="9-0-3" class="">
                <div class="layui-table-cell laytable-cell-9-0-3">综合性期刊</div>
            </td>
            <td data-field="分级" data-key="9-0-4" class="">
                <div class="layui-table-cell laytable-cell-9-0-4">A</div>
            </td>
        </tr>
        <tr data-index="2" class="">
            <td data-field="期刊全称" data-key="9-0-0" class="">
                <div class="layui-table-cell laytable-cell-9-0-0">CELL</div>
            </td>
            <td data-field="期刊简称" data-key="9-0-1" class="">
                <div class="layui-table-cell laytable-cell-9-0-1">CELL</div>
            </td>
            <td data-field="issn" data-key="9-0-2" class="">
                <div class="layui-table-cell laytable-cell-9-0-2">0092-8674</div>
            </td>
            <td data-field="学科大类" data-key="9-0-3" class="">
                <div class="layui-table-cell laytable-cell-9-0-3">生物学</div>
            </td>
            <td data-field="分级" data-key="9-0-4" class="">
                <div class="layui-table-cell laytable-cell-9-0-4">A</div>
            </td>
        </tr>
    </tbody>
    '''
    
    print("=" * 60)
    print("测试期刊数据解析功能")
    print("=" * 60)
    
    soup = BeautifulSoup(html_data, 'html.parser')
    
    # 查找所有数据行
    data_rows = soup.find_all('tr')
    print(f"找到 {len(data_rows)} 行数据")
    
    journals = []
    
    for i, row in enumerate(data_rows):
        try:
            journal_info = {}
            
            # 根据实际HTML结构提取数据
            # <td data-field="期刊全称">
            期刊全称_td = row.find('td', {'data-field': '期刊全称'})
            if 期刊全称_td:
                div = 期刊全称_td.find('div', class_='layui-table-cell')
                if div:
                    journal_info['期刊全称'] = clean_text(div.get_text())
            
            # <td data-field="期刊简称">
            期刊简称_td = row.find('td', {'data-field': '期刊简称'})
            if 期刊简称_td:
                div = 期刊简称_td.find('div', class_='layui-table-cell')
                if div:
                    journal_info['期刊简称'] = clean_text(div.get_text())
            
            # <td data-field="issn">
            issn_td = row.find('td', {'data-field': 'issn'})
            if issn_td:
                div = issn_td.find('div', class_='layui-table-cell')
                if div:
                    journal_info['ISSN'] = clean_text(div.get_text())
            
            # <td data-field="学科大类">
            学科大类_td = row.find('td', {'data-field': '学科大类'})
            if 学科大类_td:
                div = 学科大类_td.find('div', class_='layui-table-cell')
                if div:
                    journal_info['学科大类'] = clean_text(div.get_text())
            
            # <td data-field="分级">
            分级_td = row.find('td', {'data-field': '分级'})
            if 分级_td:
                div = 分级_td.find('div', class_='layui-table-cell')
                if div:
                    journal_info['分级'] = clean_text(div.get_text())
            
            # 添加行索引
            data_index = row.get('data-index', str(i))
            journal_info['序号'] = data_index
            
            # 只保存有期刊全称的记录
            if journal_info.get('期刊全称'):
                journals.append(journal_info)
                print(f"解析第{i+1}条: {journal_info}")
        
        except Exception as e:
            print(f"解析第{i+1}行时出错: {e}")
            continue
    
    print(f"\n✓ 成功解析 {len(journals)} 条期刊数据")
    
    # 测试保存到Excel
    try:
        import pandas as pd
        
        if journals:
            df = pd.DataFrame(journals)
            df.to_excel('测试期刊数据.xlsx', index=False, engine='openpyxl')
            print("✓ 测试数据已保存到 测试期刊数据.xlsx")
            
            print("\n数据预览:")
            print(df)
        
    except ImportError:
        print("pandas未安装，跳过Excel保存测试")
    except Exception as e:
        print(f"保存Excel时出错: {e}")
    
    return journals

def main():
    test_parse_html()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版爬虫 - 专门用于分析页面结构和调试
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from bs4 import BeautifulSoup
import time
import json

class DebugCrawler:
    def __init__(self):
        self.url = "https://ir.scu.edu.cn/style/excel/views/index3.jsp"
        self.driver = None
    
    def setup_driver(self):
        """设置浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--window-size=1920,1080')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            print("✓ 浏览器驱动设置成功")
            return True
        except Exception as e:
            print(f"✗ 浏览器驱动设置失败: {e}")
            return False
    
    def analyze_page_structure(self):
        """分析页面结构"""
        try:
            print("正在访问页面...")
            self.driver.get(self.url)
            time.sleep(5)
            
            # 保存页面源码
            with open('debug_page_source.html', 'w', encoding='utf-8') as f:
                f.write(self.driver.page_source)
            print("✓ 页面源码已保存到 debug_page_source.html")
            
            # 解析页面
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # 分析表单
            print("\n=== 表单分析 ===")
            forms = soup.find_all('form')
            print(f"找到 {len(forms)} 个表单")
            
            for i, form in enumerate(forms):
                print(f"\n表单 {i+1}:")
                print(f"  action: {form.get('action', 'N/A')}")
                print(f"  method: {form.get('method', 'N/A')}")
                
                # 表单中的输入框
                inputs = form.find_all('input')
                print(f"  包含 {len(inputs)} 个输入框:")
                for inp in inputs:
                    name = inp.get('name', 'N/A')
                    input_type = inp.get('type', 'N/A')
                    placeholder = inp.get('placeholder', 'N/A')
                    value = inp.get('value', 'N/A')
                    print(f"    - name='{name}', type='{input_type}', placeholder='{placeholder}', value='{value}'")
            
            # 分析所有输入框
            print("\n=== 所有输入框分析 ===")
            all_inputs = soup.find_all('input')
            print(f"页面总共有 {len(all_inputs)} 个输入框")
            
            input_info = []
            for inp in all_inputs:
                info = {
                    'name': inp.get('name', ''),
                    'type': inp.get('type', ''),
                    'placeholder': inp.get('placeholder', ''),
                    'value': inp.get('value', ''),
                    'id': inp.get('id', ''),
                    'class': inp.get('class', [])
                }
                input_info.append(info)
                print(f"  {info}")
            
            # 保存输入框信息到JSON
            with open('input_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(input_info, f, ensure_ascii=False, indent=2)
            print("✓ 输入框分析结果已保存到 input_analysis.json")
            
            # 分析表格
            print("\n=== 表格分析 ===")
            tables = soup.find_all('table')
            print(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables):
                print(f"\n表格 {i+1}:")
                print(f"  class: {table.get('class', 'N/A')}")
                print(f"  id: {table.get('id', 'N/A')}")
                
                rows = table.find_all('tr')
                print(f"  包含 {len(rows)} 行")
                
                if rows:
                    # 分析表头
                    headers = rows[0].find_all(['th', 'td'])
                    print(f"  表头有 {len(headers)} 列:")
                    for j, header in enumerate(headers):
                        print(f"    列{j+1}: {header.get_text().strip()}")
            
            # 分析列表
            print("\n=== 列表分析 ===")
            lists = soup.find_all(['ul', 'ol'])
            print(f"找到 {len(lists)} 个列表")
            
            for i, ul in enumerate(lists):
                print(f"\n列表 {i+1}:")
                print(f"  class: {ul.get('class', 'N/A')}")
                print(f"  id: {ul.get('id', 'N/A')}")
                
                items = ul.find_all('li')
                print(f"  包含 {len(items)} 个列表项:")
                for j, item in enumerate(items[:10]):  # 只显示前10个
                    text = item.get_text().strip()
                    if text:
                        print(f"    {j+1}. {text}")
            
            # 分析按钮
            print("\n=== 按钮分析 ===")
            buttons = soup.find_all(['button', 'input[type="button"]', 'input[type="submit"]'])
            print(f"找到 {len(buttons)} 个按钮")
            
            for i, btn in enumerate(buttons):
                print(f"  按钮 {i+1}:")
                print(f"    text: {btn.get_text().strip()}")
                print(f"    type: {btn.get('type', 'N/A')}")
                print(f"    value: {btn.get('value', 'N/A')}")
                print(f"    onclick: {btn.get('onclick', 'N/A')}")
            
            print("\n=== 分析完成 ===")
            print("请查看生成的文件:")
            print("- debug_page_source.html: 完整页面源码")
            print("- input_analysis.json: 输入框详细分析")
            
            return True
            
        except Exception as e:
            print(f"✗ 页面分析失败: {e}")
            return False
    
    def interactive_debug(self):
        """交互式调试"""
        try:
            print("进入交互式调试模式...")
            print("浏览器窗口将保持打开，您可以手动操作")
            
            while True:
                print("\n选择操作:")
                print("1. 重新分析当前页面")
                print("2. 保存当前页面源码")
                print("3. 查找元素")
                print("4. 退出")
                
                choice = input("请选择 (1-4): ").strip()
                
                if choice == "1":
                    self.analyze_current_page()
                elif choice == "2":
                    self.save_current_page()
                elif choice == "3":
                    self.find_element_interactive()
                elif choice == "4":
                    break
                else:
                    print("无效选择")
                    
        except KeyboardInterrupt:
            print("\n用户中断")
    
    def analyze_current_page(self):
        """分析当前页面"""
        soup = BeautifulSoup(self.driver.page_source, 'html.parser')
        
        # 快速分析
        forms = len(soup.find_all('form'))
        inputs = len(soup.find_all('input'))
        tables = len(soup.find_all('table'))
        buttons = len(soup.find_all(['button', 'input[type="button"]', 'input[type="submit"]']))
        
        print(f"当前页面: {self.driver.current_url}")
        print(f"页面标题: {self.driver.title}")
        print(f"表单: {forms}个, 输入框: {inputs}个, 表格: {tables}个, 按钮: {buttons}个")
    
    def save_current_page(self):
        """保存当前页面"""
        filename = f"page_{int(time.time())}.html"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(self.driver.page_source)
        print(f"✓ 页面已保存到 {filename}")
    
    def find_element_interactive(self):
        """交互式查找元素"""
        selector = input("请输入CSS选择器或XPath: ").strip()
        if not selector:
            return
        
        try:
            if selector.startswith('//'):
                elements = self.driver.find_elements(By.XPATH, selector)
            else:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
            
            print(f"找到 {len(elements)} 个元素")
            for i, elem in enumerate(elements[:5]):  # 只显示前5个
                print(f"  元素 {i+1}: {elem.tag_name}, text='{elem.text[:50]}'")
                
        except Exception as e:
            print(f"查找失败: {e}")
    
    def run(self):
        """运行调试器"""
        if not self.setup_driver():
            return
        
        try:
            # 先分析页面结构
            if self.analyze_page_structure():
                # 然后进入交互模式
                self.interactive_debug()
        finally:
            if self.driver:
                self.driver.quit()

def main():
    print("=" * 60)
    print("四川大学期刊系统 - 调试工具")
    print("=" * 60)
    
    debugger = DebugCrawler()
    debugger.run()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试数据获取
"""

import requests
import json

def test_direct_data_access():
    """测试直接访问数据接口"""
    print("=" * 50)
    print("测试直接访问数据接口")
    print("=" * 50)
    
    # 从页面分析中发现的数据URL
    data_urls = [
        "https://ir.scu.edu.cn/style/d2.json",
        "https://ir.scu.edu.cn/style/d.json"
    ]
    
    for url in data_urls:
        print(f"\n尝试访问: {url}")
        try:
            response = requests.get(url, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✓ 成功获取JSON数据，共 {len(data)} 条记录")
                    
                    if data:
                        # 显示第一条数据的结构
                        print("第一条数据结构:")
                        first_item = data[0]
                        for key, value in first_item.items():
                            print(f"  {key}: {value}")
                        
                        # 保存数据
                        filename = f"direct_data_{url.split('/')[-1]}"
                        with open(filename, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        print(f"✓ 数据已保存到 {filename}")
                        
                        return data
                        
                except json.JSONDecodeError:
                    print("✗ 响应不是有效的JSON格式")
                    print(f"响应内容: {response.text[:200]}...")
            else:
                print(f"✗ 请求失败: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"✗ 请求异常: {e}")
    
    return None

def test_with_session():
    """使用session测试（模拟登录状态）"""
    print("\n" + "=" * 50)
    print("使用Session测试")
    print("=" * 50)
    
    session = requests.Session()
    
    # 设置常见的请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://ir.scu.edu.cn/publicUser/toLogin'
    }
    session.headers.update(headers)
    
    data_urls = [
        "https://ir.scu.edu.cn/style/d2.json",
        "https://ir.scu.edu.cn/style/d.json"
    ]
    
    for url in data_urls:
        print(f"\n尝试访问: {url}")
        try:
            response = session.get(url, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✓ 成功获取JSON数据，共 {len(data)} 条记录")
                    
                    if data:
                        # 显示前3条数据
                        print("前3条数据:")
                        for i, item in enumerate(data[:3]):
                            print(f"  第{i+1}条: {item}")
                        
                        return data
                        
                except json.JSONDecodeError:
                    print("✗ 响应不是有效的JSON格式")
                    print(f"响应内容: {response.text[:200]}...")
            else:
                print(f"✗ 请求失败: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"✗ 请求异常: {e}")
    
    return None

def analyze_saved_page():
    """分析已保存的页面，查找可能的数据"""
    print("\n" + "=" * 50)
    print("分析已保存页面中的数据")
    print("=" * 50)
    
    try:
        with open('current_page.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找可能的JSON数据
        import re
        
        # 查找JavaScript中的数组数据
        patterns = [
            r'json\s*=\s*(\[.*?\]);',
            r'data\s*=\s*(\[.*?\]);',
            r'var\s+\w+\s*=\s*(\[.*?\]);'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            if matches:
                print(f"找到匹配模式: {pattern}")
                for i, match in enumerate(matches):
                    print(f"匹配 {i+1}: {match[:100]}...")
                    
                    try:
                        data = json.loads(match)
                        if isinstance(data, list) and len(data) > 0:
                            print(f"✓ 解析成功，包含 {len(data)} 条数据")
                            
                            # 检查数据结构
                            if isinstance(data[0], dict):
                                print("数据字段:", list(data[0].keys()))
                                return data
                    except:
                        continue
        
        print("✗ 未在页面中找到有效的JSON数据")
        
    except FileNotFoundError:
        print("✗ 未找到 current_page.html 文件")
    except Exception as e:
        print(f"✗ 分析页面失败: {e}")
    
    return None

def main():
    print("🔍 四川大学期刊数据获取测试")
    
    # 方法1：直接访问数据接口
    data = test_direct_data_access()
    if data:
        print("\n🎉 直接访问成功！")
        return
    
    # 方法2：使用session访问
    data = test_with_session()
    if data:
        print("\n🎉 Session访问成功！")
        return
    
    # 方法3：分析已保存的页面
    data = analyze_saved_page()
    if data:
        print("\n🎉 页面分析成功！")
        return
    
    print("\n❌ 所有方法都失败了")
    print("\n💡 建议:")
    print("1. 数据接口可能需要登录状态")
    print("2. 可能有反爬虫机制")
    print("3. 需要使用浏览器自动化获取数据")

if __name__ == "__main__":
    main()

@echo off
echo ====================================
echo 四川大学期刊爬虫 - 依赖安装脚本
echo ====================================

echo.
echo 正在安装Python依赖包...
pip install -r requirements.txt

echo.
echo 检查依赖包安装状态...
python -c "import requests, pandas, bs4, selenium, openpyxl; print('✓ 所有依赖包安装成功')" 2>nul || (
    echo ✗ 部分依赖包安装失败，尝试使用国内镜像源...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
)

echo.
echo ====================================
echo 安装完成！
echo ====================================
echo.
echo 注意事项：
echo 1. 请确保已安装Chrome浏览器
echo 2. 请下载对应版本的ChromeDriver并放到系统PATH中
echo 3. ChromeDriver下载地址：https://chromedriver.chromium.org/
echo.
echo 运行程序：python find.py
echo.
pause

# 四川大学期刊爬虫 - 最终版本使用说明

## 🎉 功能验证完成

经过页面结构分析和测试，爬虫已经能够正确解析期刊数据！

### ✅ 已验证功能

1. **登录表单识别** ✓
   - 用户名输入框：`#userName`
   - 密码输入框：`#password`
   - 验证码输入框：`#randomCode`
   - 登录按钮：`#loginbtn1`

2. **数据解析功能** ✓
   - 正确识别layui表格结构
   - 准确提取期刊数据字段
   - 测试解析了3条样本数据，完全正确

3. **数据字段** ✓
   - 期刊全称
   - 期刊简称
   - ISSN
   - 学科大类
   - 分级
   - 序号

## 🚀 推荐使用方式

### 方法1：使用优化版爬虫（推荐）
```bash
python scu_journal_crawler.py
```

### 方法2：先测试再使用
```bash
# 1. 测试数据解析功能
python test_parser.py

# 2. 分析页面结构
python simple_test.py

# 3. 运行完整爬虫
python scu_journal_crawler.py
```

## 📋 使用步骤

### 1. 环境准备
```bash
# 安装依赖
pip install requests pandas beautifulsoup4 selenium openpyxl

# 下载ChromeDriver
# 访问 https://chromedriver.chromium.org/
# 下载与Chrome版本匹配的驱动
```

### 2. 运行程序
```bash
python scu_journal_crawler.py
```

### 3. 输入登录信息
- **用户名**：您的一卡通号
- **密码**：图书馆借阅密码

### 4. 处理验证码
- 程序会自动打开浏览器
- 手动输入验证码
- 在控制台按回车继续

### 5. 等待爬取完成
- 程序会自动处理分页
- 获取所有期刊数据
- 保存到Excel文件

## 📊 输出文件

爬取完成后会生成：
- `四川大学期刊数据.xlsx` - Excel格式数据
- `四川大学期刊数据.csv` - CSV格式数据
- `current_page.html` - 页面源码（调试用）

## 📈 数据示例

根据测试结果，每条期刊记录包含：

| 序号 | 期刊全称 | 期刊简称 | ISSN | 学科大类 | 分级 |
|------|----------|----------|------|----------|------|
| 0 | NATURE | NATURE | 0028-0836 | 综合性期刊 | A |
| 1 | SCIENCE | SCIENCE | 0036-8075 | 综合性期刊 | A |
| 2 | CELL | CELL | 0092-8674 | 生物学 | A |

## 🔧 技术特点

### 智能识别
- 基于实际HTML结构分析
- 准确定位layui表格元素
- 使用data-field属性精确提取数据

### 自动化处理
- 自动登录和验证码处理
- 自动分页获取所有数据
- 智能等待页面加载

### 容错机制
- 多种选择器备选方案
- 详细的错误提示
- 调试文件自动生成

## ⚠️ 注意事项

### 登录要求
- 需要有效的四川大学账号
- 确保账号有访问权限

### 环境要求
- Chrome浏览器（最新版本）
- ChromeDriver（版本匹配）
- 稳定的网络连接

### 使用规范
- 遵守网站使用条款
- 合理控制爬取频率
- 仅用于学术研究目的

## 🐛 故障排除

### 常见问题及解决方案

1. **ChromeDriver版本不匹配**
   ```bash
   # 查看Chrome版本
   chrome://version/
   
   # 下载对应版本ChromeDriver
   https://chromedriver.chromium.org/
   ```

2. **登录失败**
   - 检查用户名和密码
   - 确认账号访问权限
   - 手动在浏览器中完成登录

3. **数据解析失败**
   - 运行 `test_parser.py` 验证解析功能
   - 检查网站结构是否变化
   - 查看生成的HTML调试文件

4. **依赖包问题**
   ```bash
   # 使用国内镜像源
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

## 📁 文件说明

### 核心程序
- `scu_journal_crawler.py` - 优化版爬虫（推荐使用）
- `find.py` - 完整版爬虫

### 测试工具
- `test_parser.py` - 数据解析功能测试
- `simple_test.py` - 页面结构分析
- `test.py` - 环境测试

### 配置文件
- `requirements.txt` - 依赖包列表
- `install.bat` - Windows安装脚本

### 文档
- `使用指南.md` - 详细使用说明
- `README.md` - 技术文档

## 🎯 成功案例

测试结果显示：
- ✅ 成功识别登录表单
- ✅ 正确解析期刊数据
- ✅ 生成标准Excel文件
- ✅ 数据字段完整准确

## 📞 技术支持

如果遇到问题：
1. 查看生成的调试文件
2. 运行测试脚本检查环境
3. 检查网站是否有结构变化

---

**祝您使用愉快！** 🎉

*本爬虫基于实际页面结构分析开发，经过充分测试验证。*
